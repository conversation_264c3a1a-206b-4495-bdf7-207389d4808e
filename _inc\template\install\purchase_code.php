<br>
<div class="container">
    <div class="row">
        <div class="col-sm-8 col-sm-offset-2">
            <div class="panel panel-default header">
                <div class="panel-heading text-center">
                    <h2><?php echo trans('text_verify_purchase_code'); ?></h2>
                    <p><?php echo trans('text_running_step_2_of_6'); ?></p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-8 col-sm-offset-2">  
            <div class="panel panel-default menubar">
                <div class="panel-heading bg-white">
                    <ul class="nav nav-pills">
                        <li>
                            <a href="index.php"><?php echo trans('text_checklist'); ?></a>
                        </li>
                        <li class="active">
                            <span class="fa fa-check"></span> <a href="purchase_code.php"> <?php echo trans('text_verification'); ?></a>
                        </li>
                        <li>
                            <a href="#"><?php echo trans('text_database'); ?></a>
                        </li>
                        <li>
                            <a href="#" onClick="return false"><?php echo trans('text_timezone'); ?></a>
                        </li>
                        <li>
                            <a href="#" onClick="return false"><?php echo trans('text_site_config'); ?></a>
                        </li>
                        <li>
                            <a href="#" onClick="return false"><?php echo trans('text_done'); ?></a>
                        </li>
                    </ul>
                </div>
                <div class="panel-body ins-bg-col">

                    <?php if($errors['internet_connection']) : ?>
                        <div class="alert alert-danger">
                            <p><?php echo $errors['internet_connection']; ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if($errors['config_error']) : ?>
                        <div class="alert alert-danger">
                            <p><?php echo $errors['config_error']; ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <div class="alert alert-info">
                        <h4><i class="fa fa-info-circle"></i> تم تخطي التحقق من رمز الشراء</h4>
                        <p>تم تعطيل التحقق من رمز الشراء. يمكنك المتابعة مباشرة إلى الخطوة التالية.</p>
                    </div>

                    <form id="purchaseCodeForm" class="form-horizontal" role="form" action="purchase_code.php" method="post">
                        <!-- حقول مخفية تحتوي على قيم وهمية -->
                        <input type="hidden" name="purchase_username" value="demo_user">
                        <input type="hidden" name="purchase_code" value="demo-purchase-code-12345">

                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                اسم المستخدم:
                            </label>
                            <div class="col-sm-7">
                                <p class="form-control-static">demo_user (تم التعيين تلقائياً)</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                رمز الشراء:
                            </label>
                            <div class="col-sm-7">
                                <p class="form-control-static">demo-purchase-code-12345 (تم التعيين تلقائياً)</p>
                            </div>
                        </div>

                        <br>

                        <div class="form-group">
                            <div class="col-sm-6 text-right">
                                <a href="index.php" class="btn btn-default">&larr; <?php echo trans('text_prev_step'); ?></a>
                            </div>
                            <div class="col-sm-6 text-left">
                                <button class="btn btn-success ajaxcall" data-form="purchaseCodeForm" data-loading-text="Processing...">المتابعة إلى قاعدة البيانات &rarr;</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="text-center copyright">&#169; <a href="<?php echo trans('text_footer_link'); ?>"><?php echo trans('text_footer_link_text'); ?></a>, <?php echo trans('text_all_right_reserved'); ?></div>
        </div>
    </div>
</div>